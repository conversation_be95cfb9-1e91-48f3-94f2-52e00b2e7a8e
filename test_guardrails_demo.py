"""
Demo file to test that CI guardrails catch long lines.
This file intentionally contains a line longer than 88 characters.
"""

# This line is intentionally longer than 88 characters to test our CI guardrails and should cause the build to fail
def demo_function_with_very_long_line_that_exceeds_eighty_eight_characters_and_should_trigger_ci_failure():
    """This function has a very long name to test CI line length checking."""
    return "This should cause CI to fail due to E501 line length violation"


def normal_function():
    """This function has a normal length name."""
    return "This is fine"
