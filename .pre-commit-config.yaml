repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        language_version: python3.9

-   repo: local
    hooks:
    -   id: line-length-check
        name: python line-length check
        entry: python
        args: ['-c', 'import sys; lines = open(sys.argv[1]).readlines(); violations = [(i+1, len(line.rstrip())) for i, line in enumerate(lines) if len(line.rstrip()) > 88]; [print(f"{sys.argv[1]}:{line}:{length}: Line too long ({length} > 88)") for line, length in violations]; sys.exit(1 if violations else 0)']
        language: system
        files: \.py$
    -   id: ts-typecheck
        name: typescript strict
        entry: bash -c "cd frontend && npm run type-check"
        language: system
        files: \.(ts|tsx)$
        pass_filenames: false
    -   id: mypy
        name: mypy
        entry: python -m mypy
        language: system
        types: [python]
        args: [
            "--show-error-codes",
            "--disallow-untyped-defs",  # Strict typing for our own code
            "--disallow-incomplete-defs",  # Ensure complete typing
            # Only selectively ignore third-party libraries without stubs
            "--no-warn-unused-ignores",
            # Specific modules to ignore import issues with
            "--ignore-missing-imports",
            # Use a more targeted approach for import-untyped warnings
            "--disable-error-code=import-untyped"
        ]
        require_serial: true
        files: ^pi_lawyer/
